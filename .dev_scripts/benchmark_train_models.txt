atss/atss_r50_fpn_1x_coco.py
faster_rcnn/faster-rcnn_r50_fpn_1x_coco.py
mask_rcnn/mask-rcnn_r50_fpn_1x_coco.py
cascade_rcnn/cascade-mask-rcnn_r50_fpn_1x_coco.py
configs/grounding_dino/grounding_dino_swin-t_finetune_16xb2_1x_coco.py
configs/glip/glip_atss_swin-t_a_fpn_dyhead_16xb2_ms-2x_funtune_coco.py
configs/ddq/ddq-detr-4scale_r50_8xb2-12e_coco.py
panoptic_fpn/panoptic-fpn_r50_fpn_1x_coco.py
retinanet/retinanet_r50_fpn_1x_coco.py
rtmdet/rtmdet_s_8xb32-300e_coco.py
rtmdet/rtmdet-ins_s_8xb32-300e_coco.py
fcos/fcos_r50-caffe_fpn_gn-head-center-normbbox-centeronreg-giou_1x_coco.py
centernet/centernet-update_r50-caffe_fpn_ms-1x_coco.py
dino/dino-4scale_r50_8xb2-12e_coco.py
htc/htc_r50_fpn_1x_coco.py
mask2former/mask2former_r50_8xb2-lsj-50e_coco-panoptic.py
swin/mask-rcnn_swin-t-p4-w7_fpn_1x_coco.py
condinst/condinst_r50_fpn_ms-poly-90k_coco_instance.py
lvis/mask-rcnn_r50_fpn_sample1e-3_ms-1x_lvis-v1.py
mask2former_vis/mask2former_r50_8xb2-8e_youtubevis2021.py
masktrack_rcnn/masktrack-rcnn_mask-rcnn_r50_fpn_8xb1-12e_youtubevis2021.py
qdtrack/qdtrack_faster-rcnn_r50_fpn_8xb2-4e_mot17halftrain_test-mot17halfval.py
