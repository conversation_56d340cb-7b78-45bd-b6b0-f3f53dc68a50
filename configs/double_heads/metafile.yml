Collections:
  - Name: Rethinking Classification and Localization for Object Detection
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: https://arxiv.org/pdf/1904.06493
      Title: 'Rethinking Classification and Localization for Object Detection'
    README: configs/double_heads/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/roi_heads/double_roi_head.py#L6
      Version: v2.0.0

Models:
  - Name: dh-faster-rcnn_r50_fpn_1x_coco
    In Collection: Rethinking Classification and Localization for Object Detection
    Config: configs/double_heads/dh-faster-rcnn_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.8
      inference time (ms/im):
        - value: 105.26
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/double_heads/dh_faster_rcnn_r50_fpn_1x_coco/dh_faster_rcnn_r50_fpn_1x_coco_20200130-586b67df.pth
