Collections:
  - Name: Deformable Convolutional Networks
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Deformable Convolution
    Paper:
      URL: https://arxiv.org/abs/1703.06211
      Title: "Deformable Convolutional Networks"
    README: configs/dcn/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/ops/dcn/deform_conv.py#L15
      Version: v2.0.0

Models:
  - Name: faster-rcnn_r50_fpn_dconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/faster-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.0
      inference time (ms/im):
        - value: 56.18
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r50_fpn_dconv_c3-c5_1x_coco/faster_rcnn_r50_fpn_dconv_c3-c5_1x_coco_20200130-d68aed1e.pth

  - Name: faster-rcnn_r50_fpn_dpool_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/faster-rcnn_r50_fpn_dpool_1x_coco.py
    Metadata:
      Training Memory (GB): 5.0
      inference time (ms/im):
        - value: 58.14
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r50_fpn_dpool_1x_coco/faster_rcnn_r50_fpn_dpool_1x_coco_20200307-90d3c01d.pth

  - Name: faster-rcnn_r101-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/faster-rcnn_r101-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 80
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r101_fpn_dconv_c3-c5_1x_coco/faster_rcnn_r101_fpn_dconv_c3-c5_1x_coco_20200203-1377f13d.pth

  - Name: faster-rcnn_x101-32x4d-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/faster-rcnn_x101-32x4d-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.3
      inference time (ms/im):
        - value: 100
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_x101_32x4d_fpn_dconv_c3-c5_1x_coco/faster_rcnn_x101_32x4d_fpn_dconv_c3-c5_1x_coco_20200203-4f85c69c.pth

  - Name: mask-rcnn_r50_fpn_dconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/mask-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.5
      inference time (ms/im):
        - value: 64.94
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/mask_rcnn_r50_fpn_dconv_c3-c5_1x_coco/mask_rcnn_r50_fpn_dconv_c3-c5_1x_coco_20200203-4d9ad43b.pth

  - Name: mask-rcnn_r50_fpn_fp16_dconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/mask-rcnn_r50-dconv-c3-c5_fpn_amp-1x_coco.py
    Metadata:
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
        - Mixed Precision Training
      Training Memory (GB): 3.0
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/fp16/mask_rcnn_r50_fpn_fp16_dconv_c3-c5_1x_coco/mask_rcnn_r50_fpn_fp16_dconv_c3-c5_1x_coco_20210520_180247-c06429d2.pth

  - Name: mask-rcnn_r101-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/mask-rcnn_r101-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.5
      inference time (ms/im):
        - value: 85.47
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/mask_rcnn_r101_fpn_dconv_c3-c5_1x_coco/mask_rcnn_r101_fpn_dconv_c3-c5_1x_coco_20200216-a71f5bce.pth

  - Name: cascade-rcnn_r50_fpn_dconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/cascade-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.5
      inference time (ms/im):
        - value: 68.49
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/cascade_rcnn_r50_fpn_dconv_c3-c5_1x_coco/cascade_rcnn_r50_fpn_dconv_c3-c5_1x_coco_20200130-2f1fca44.pth

  - Name: cascade-rcnn_r101-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/cascade-rcnn_r101-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.4
      inference time (ms/im):
        - value: 90.91
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/cascade_rcnn_r101_fpn_dconv_c3-c5_1x_coco/cascade_rcnn_r101_fpn_dconv_c3-c5_1x_coco_20200203-3b2f0594.pth

  - Name: cascade-mask-rcnn_r50_fpn_dconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/cascade-mask-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 100
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/cascade_mask_rcnn_r50_fpn_dconv_c3-c5_1x_coco/cascade_mask_rcnn_r50_fpn_dconv_c3-c5_1x_coco_20200202-42e767a2.pth

  - Name: cascade-mask-rcnn_r101-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/cascade-mask-rcnn_r101-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.0
      inference time (ms/im):
        - value: 116.28
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/cascade_mask_rcnn_r101_fpn_dconv_c3-c5_1x_coco/cascade_mask_rcnn_r101_fpn_dconv_c3-c5_1x_coco_20200204-df0c5f10.pth

  - Name: cascade-mask-rcnn_x101-32x4d-dconv-c3-c5_fpn_1x_coco
    In Collection: Deformable Convolutional Networks
    Config: configs/dcn/cascade-mask-rcnn_x101-32x4d-dconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 9.2
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 47.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 41.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/cascade_mask_rcnn_x101_32x4d_fpn_dconv_c3-c5_1x_coco/cascade_mask_rcnn_x101_32x4d_fpn_dconv_c3-c5_1x_coco-e75f90c8.pth
