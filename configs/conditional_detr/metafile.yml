Collections:
  - Name: Conditional DETR
    Metadata:
      Training Data: COCO
      Training Techniques:
        - AdamW
        - Multi Scale Train
        - Gradient Clip
      Training Resources: 8x A100 GPUs
      Architecture:
        - ResNet
        - Transformer
    Paper:
      URL: https://arxiv.org/abs/2108.06152
      Title: 'Conditional DETR for Fast Training Convergence'
    README: configs/conditional_detr/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/f4112c9e5611468ffbd57cfba548fd1289264b52/mmdet/models/detectors/conditional_detr.py#L14
      Version: v3.0.0rc6

Models:
  - Name: conditional-detr_r50_8xb2-50e_coco
    In Collection: Conditional DETR
    Config: configs/conditional_detr/conditional-detr_r50_8xb2-50e_coco.py
    Metadata:
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.9
    Weights: https://download.openmmlab.com/mmdetection/v3.0/conditional_detr/conditional-detr_r50_8xb2-50e_coco/conditional-detr_r50_8xb2-50e_coco_20221121_180202-c83a1dc0.pth
