Collections:
  - Name: Cascade R-CNN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Cascade R-CNN
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: http://dx.doi.org/10.1109/tpami.2019.2956516
      Title: 'Cascade R-CNN: Delving into High Quality Object Detection'
    README: configs/cascade_rcnn/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/detectors/cascade_rcnn.py#L6
      Version: v2.0.0
  - Name: Cascade Mask R-CNN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Cascade R-CNN
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: http://dx.doi.org/10.1109/tpami.2019.2956516
      Title: 'Cascade R-CNN: Delving into High Quality Object Detection'
    README: configs/cascade_rcnn/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/detectors/cascade_rcnn.py#L6
      Version: v2.0.0

Models:
  - Name: cascade-rcnn_r50-caffe_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.2
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r50_caffe_fpn_1x_coco/cascade_rcnn_r50_caffe_fpn_1x_coco_bbox_mAP-0.404_20200504_174853-b857be87.pth

  - Name: cascade-rcnn_r50_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.4
      inference time (ms/im):
        - value: 62.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r50_fpn_1x_coco/cascade_rcnn_r50_fpn_1x_coco_20200316-3dc56deb.pth

  - Name: cascade-rcnn_r50_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r50_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 4.4
      inference time (ms/im):
        - value: 62.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r50_fpn_20e_coco/cascade_rcnn_r50_fpn_20e_coco_bbox_mAP-0.41_20200504_175131-e9872a90.pth

  - Name: cascade-rcnn_r101-caffe_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r101-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.2
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r101_caffe_fpn_1x_coco/cascade_rcnn_r101_caffe_fpn_1x_coco_bbox_mAP-0.423_20200504_175649-cab8dbd5.pth

  - Name: cascade-rcnn_r101_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r101_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.4
      inference time (ms/im):
        - value: 74.07
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r101_fpn_1x_coco/cascade_rcnn_r101_fpn_1x_coco_20200317-0b6a2fbf.pth

  - Name: cascade-rcnn_r101_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_r101_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 6.4
      inference time (ms/im):
        - value: 74.07
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_r101_fpn_20e_coco/cascade_rcnn_r101_fpn_20e_coco_bbox_mAP-0.425_20200504_231812-5057dcc5.pth

  - Name: cascade-rcnn_x101-32x4d_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.6
      inference time (ms/im):
        - value: 91.74
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_x101_32x4d_fpn_1x_coco/cascade_rcnn_x101_32x4d_fpn_1x_coco_20200316-95c2deb6.pth

  - Name: cascade-rcnn_x101-32x4d_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_x101-32x4d_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 7.6
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_x101_32x4d_fpn_20e_coco/cascade_rcnn_x101_32x4d_fpn_20e_coco_20200906_134608-9ae0a720.pth

  - Name: cascade-rcnn_x101-64x4d_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 10.7
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_x101_64x4d_fpn_1x_coco/cascade_rcnn_x101_64x4d_fpn_1x_coco_20200515_075702-43ce6a30.pth

  - Name: cascade-rcnn_x101_64x4d_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-rcnn_x101_64x4d_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 10.7
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_rcnn_x101_64x4d_fpn_20e_coco/cascade_rcnn_x101_64x4d_fpn_20e_coco_20200509_224357-051557b1.pth

  - Name: cascade-mask-rcnn_r50-caffe_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 5.9
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  36.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r50_caffe_fpn_1x_coco/cascade_mask_rcnn_r50_caffe_fpn_1x_coco_bbox_mAP-0.412__segm_mAP-0.36_20200504_174659-5004b251.pth

  - Name: cascade-mask-rcnn_r50_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 89.29
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  35.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r50_fpn_1x_coco/cascade_mask_rcnn_r50_fpn_1x_coco_20200203-9d4dcb24.pth

  - Name: cascade-mask-rcnn_r50_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r50_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 6.0
      inference time (ms/im):
        - value: 89.29
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  36.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r50_fpn_20e_coco/cascade_mask_rcnn_r50_fpn_20e_coco_bbox_mAP-0.419__segm_mAP-0.365_20200504_174711-4af8e66e.pth

  - Name: cascade-mask-rcnn_r101-caffe_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r101-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.8
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r101_caffe_fpn_1x_coco/cascade_mask_rcnn_r101_caffe_fpn_1x_coco_bbox_mAP-0.432__segm_mAP-0.376_20200504_174813-5c1e9599.pth

  - Name: cascade-mask-rcnn_r101_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r101_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.9
      inference time (ms/im):
        - value: 102.04
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r101_fpn_1x_coco/cascade_mask_rcnn_r101_fpn_1x_coco_20200203-befdf6ee.pth

  - Name: cascade-mask-rcnn_r101_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r101_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 7.9
      inference time (ms/im):
        - value: 102.04
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  37.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r101_fpn_20e_coco/cascade_mask_rcnn_r101_fpn_20e_coco_bbox_mAP-0.434__segm_mAP-0.378_20200504_174836-005947da.pth

  - Name: cascade-mask-rcnn_x101-32x4d_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 9.2
      inference time (ms/im):
        - value: 116.28
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  38.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_32x4d_fpn_1x_coco/cascade_mask_rcnn_x101_32x4d_fpn_1x_coco_20200201-0f411b1f.pth

  - Name: cascade-mask-rcnn_x101-32x4d_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-32x4d_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 9.2
      inference time (ms/im):
        - value: 116.28
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  39.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_32x4d_fpn_20e_coco/cascade_mask_rcnn_x101_32x4d_fpn_20e_coco_20200528_083917-ed1f4751.pth

  - Name: cascade-mask-rcnn_x101-64x4d_fpn_1x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 12.2
      inference time (ms/im):
        - value: 149.25
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  39.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_64x4d_fpn_1x_coco/cascade_mask_rcnn_x101_64x4d_fpn_1x_coco_20200203-9a2db89d.pth

  - Name: cascade-mask-rcnn_x101-64x4d_fpn_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-64x4d_fpn_20e_coco.py
    Metadata:
      Training Memory (GB): 12.2
      Epochs: 20
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_64x4d_fpn_20e_coco/cascade_mask_rcnn_x101_64x4d_fpn_20e_coco_20200512_161033-bdb5126a.pth

  - Name: cascade-mask-rcnn_r50-caffe_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r50-caffe_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 5.7
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r50_caffe_fpn_mstrain_3x_coco/cascade_mask_rcnn_r50_caffe_fpn_mstrain_3x_coco_20210707_002651-6e29b3a6.pth

  - Name: cascade-mask-rcnn_r50_fpn_mstrain_3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r50_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 5.9
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r50_fpn_mstrain_3x_coco/cascade_mask_rcnn_r50_fpn_mstrain_3x_coco_20210628_164719-5bdc3824.pth

  - Name: cascade-mask-rcnn_r101-caffe_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r101-caffe_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 7.7
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r101_caffe_fpn_mstrain_3x_coco/cascade_mask_rcnn_r101_caffe_fpn_mstrain_3x_coco_20210707_002620-a5bd2389.pth

  - Name: cascade-mask-rcnn_r101_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_r101_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 7.8
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_r101_fpn_mstrain_3x_coco/cascade_mask_rcnn_r101_fpn_mstrain_3x_coco_20210628_165236-51a2d363.pth

  - Name: cascade-mask-rcnn_x101-32x4d_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-32x4d_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 9.0
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_32x4d_fpn_mstrain_3x_coco/cascade_mask_rcnn_x101_32x4d_fpn_mstrain_3x_coco_20210706_225234-40773067.pth

  - Name: cascade-mask-rcnn_x101-32x8d_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-32x8d_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 12.1
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_32x8d_fpn_mstrain_3x_coco/cascade_mask_rcnn_x101_32x8d_fpn_mstrain_3x_coco_20210719_180640-9ff7e76f.pth

  - Name: cascade-mask-rcnn_x101-64x4d_fpn_ms-3x_coco
    In Collection: Cascade R-CNN
    Config: configs/cascade_rcnn/cascade-mask-rcnn_x101-64x4d_fpn_ms-3x_coco.py
    Metadata:
      Training Memory (GB): 12.0
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/cascade_rcnn/cascade_mask_rcnn_x101_64x4d_fpn_mstrain_3x_coco/cascade_mask_rcnn_x101_64x4d_fpn_mstrain_3x_coco_20210719_210311-d3e64ba0.pth
