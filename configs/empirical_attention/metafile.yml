Collections:
  - Name: Empirical Attention
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Deformable Convolution
        - FPN
        - RPN
        - ResNet
        - RoIAlign
        - Spatial Attention
    Paper:
      URL: https://arxiv.org/pdf/1904.05873
      Title: 'An Empirical Study of Spatial Attention Mechanisms in Deep Networks'
    README: configs/empirical_attention/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/ops/generalized_attention.py#L10
      Version: v2.0.0

Models:
  - Name: faster-rcnn_r50_fpn_attention_1111_1x_coco
    In Collection: Empirical Attention
    Config: configs/empirical_attention/faster-rcnn_r50-attn1111_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.0
      inference time (ms/im):
        - value: 72.46
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/empirical_attention/faster_rcnn_r50_fpn_attention_1111_1x_coco/faster_rcnn_r50_fpn_attention_1111_1x_coco_20200130-403cccba.pth

  - Name: faster-rcnn_r50_fpn_attention_0010_1x_coco
    In Collection: Empirical Attention
    Config: configs/empirical_attention/faster-rcnn_r50-attn0010_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.2
      inference time (ms/im):
        - value: 54.35
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/empirical_attention/faster_rcnn_r50_fpn_attention_0010_1x_coco/faster_rcnn_r50_fpn_attention_0010_1x_coco_20200130-7cb0c14d.pth

  - Name: faster-rcnn_r50_fpn_attention_1111_dcn_1x_coco
    In Collection: Empirical Attention
    Config: configs/empirical_attention/faster-rcnn_r50-attn1111-dcn_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 8.0
      inference time (ms/im):
        - value: 78.74
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/empirical_attention/faster_rcnn_r50_fpn_attention_1111_dcn_1x_coco/faster_rcnn_r50_fpn_attention_1111_dcn_1x_coco_20200130-8b2523a6.pth

  - Name: faster-rcnn_r50_fpn_attention_0010_dcn_1x_coco
    In Collection: Empirical Attention
    Config: configs/empirical_attention/faster-rcnn_r50-attn0010-dcn_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.2
      inference time (ms/im):
        - value: 58.48
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/empirical_attention/faster_rcnn_r50_fpn_attention_0010_dcn_1x_coco/faster_rcnn_r50_fpn_attention_0010_dcn_1x_coco_20200130-1a2e831d.pth
