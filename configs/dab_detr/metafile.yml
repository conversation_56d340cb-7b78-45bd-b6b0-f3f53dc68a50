Collections:
  - Name: DAB-<PERSON><PERSON>
    Metadata:
      Training Data: COCO
      Training Techniques:
        - AdamW
        - Multi Scale Train
        - Gradient Clip
      Training Resources: 8x A100 GPUs
      Architecture:
        - ResNet
        - Transformer
    Paper:
      URL: https://arxiv.org/abs/2201.12329
      Title: 'DAB-DETR: Dynamic Anchor Boxes are Better Queries for DETR'
    README: configs/dab_detr/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/f4112c9e5611468ffbd57cfba548fd1289264b52/mmdet/models/detectors/dab_detr.py#L15
      Version: v3.0.0rc6

Models:
  - Name: dab-detr_r50_8xb2-50e_coco
    In Collection: DAB-DETR
    Config: configs/dab_detr/dab-detr_r50_8xb2-50e_coco.py
    Metadata:
      Epochs: 50
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.3
    Weights: https://download.openmmlab.com/mmdetection/v3.0/dab_detr/dab-detr_r50_8xb2-50e_coco/dab-detr_r50_8xb2-50e_coco_20221122_120837-c1035c8c.pth
