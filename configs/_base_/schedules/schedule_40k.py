# iter-based 训练配置
train_cfg = dict(
    type='IterBasedTrainLoop',  # iter-based 训练循环
    max_iters=40000,  # 最大迭代次数
    val_interval=2000)  # 每隔多少次进行一次验证
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 将参数调度器修改为 iter-based
param_scheduler = [
    dict(
        type='LinearLR', start_factor=0.001, by_epoch=False, begin=0, end=500),
    dict(
        type='MultiStepLR',
        begin=0,
        end=40000,
        by_epoch=False,
        milestones=[20000, 30000],
        gamma=0.1)
]

# 优化器配置
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='SGD', lr=0.02, momentum=0.9, weight_decay=0.0001),
    clip_grad=dict(max_norm=35, norm_type=2))
