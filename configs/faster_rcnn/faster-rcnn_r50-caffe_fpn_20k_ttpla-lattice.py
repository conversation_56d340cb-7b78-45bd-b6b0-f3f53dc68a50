_base_ = 'faster-rcnn_r50-caffe_fpn_1x_ttpla.py'
max_iter = 20000

param_scheduler = [
    dict(
        type='LinearLR', start_factor=0.001, by_epoch=False, begin=0, end=400),
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_iter,
        by_epoch=False,
        milestones=[0.667*max_iter, 0.833*max_iter],
        gamma=0.1)
]

train_cfg = dict(
    _delete_=True,
    type='IterBasedTrainLoop',
    max_iters=max_iter,
    val_interval=2000)
default_hooks = dict(checkpoint=dict(by_epoch=False, interval=2000))
log_processor = dict(by_epoch=False)

#只保留需要的类别
classes = ('tower_lattice')
train_dataloader = dict(
    dataset=dict(
        metainfo=dict(classes=classes))
    )
val_dataloader = dict(
    dataset=dict(
        metainfo=dict(classes=classes))
    )
test_dataloader = dict(
    dataset=dict(
        metainfo=dict(classes=classes))
    )

# 优化器配置
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(type='SGD', lr=0.01, momentum=0.9, weight_decay=0.0001),
    clip_grad=dict(max_norm=35, norm_type=2))

# 将所有的 `num_classes` 默认值修改为 5（原来为80）
model = dict(
    roi_head=dict(
        bbox_head=dict(num_classes=1)
        )
    )
    # mask_head=dict(num_classes=4)))